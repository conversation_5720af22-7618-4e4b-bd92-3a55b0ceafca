#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理客户分析数据脚本
删除刚刚导入的客户分析数据（基于import_time字段）

使用方法:
python cleanup_customer_data.py

作者: AI Assistant
创建时间: 2025-07-30
"""

import pymysql
import logging
from datetime import datetime, timedelta
import sys

# 尝试导入配置文件
try:
    from config_template import DB_CONFIG
    print("使用配置文件 config_template.py")
except ImportError:
    print("未找到配置文件，使用默认配置")
    DB_CONFIG = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '',
        'database': 'dyredbook',
        'charset': 'utf8mb4'
    }

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cleanup_log.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def connect_to_database():
    """连接到MySQL数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logging.info("数据库连接成功")
        return connection
    except Exception as e:
        logging.error(f"数据库连接失败: {e}")
        raise

def cleanup_recent_data(connection, hours_back=2):
    """
    删除最近几小时内导入的数据
    
    Args:
        connection: 数据库连接
        hours_back: 删除多少小时内的数据，默认2小时
    """
    try:
        cursor = connection.cursor()
        
        # 计算时间范围
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        
        # 先查询要删除的数据数量
        count_sql = """
        SELECT COUNT(*) FROM customer_analysis_results 
        WHERE import_time >= %s
        """
        cursor.execute(count_sql, (cutoff_time,))
        count = cursor.fetchone()[0]
        
        if count == 0:
            logging.info(f"没有找到{hours_back}小时内导入的数据")
            return 0
        
        logging.info(f"找到 {count} 条最近{hours_back}小时内导入的数据")
        
        # 确认删除
        confirm = input(f"确认删除这 {count} 条记录吗？(y/N): ")
        if confirm.lower() != 'y':
            logging.info("用户取消删除操作")
            return 0
        
        # 执行删除
        delete_sql = """
        DELETE FROM customer_analysis_results 
        WHERE import_time >= %s
        """
        cursor.execute(delete_sql, (cutoff_time,))
        connection.commit()
        
        deleted_count = cursor.rowcount
        logging.info(f"成功删除 {deleted_count} 条记录")
        
        cursor.close()
        return deleted_count
        
    except Exception as e:
        connection.rollback()
        logging.error(f"删除数据失败: {e}")
        raise

def cleanup_by_signature(connection):
    """
    删除signature字段为空字符串的数据（刚刚错误导入的数据）
    """
    try:
        cursor = connection.cursor()
        
        # 先查询要删除的数据数量
        count_sql = """
        SELECT COUNT(*) FROM customer_analysis_results 
        WHERE signature = ''
        """
        cursor.execute(count_sql)
        count = cursor.fetchone()[0]
        
        if count == 0:
            logging.info("没有找到signature为空字符串的数据")
            return 0
        
        logging.info(f"找到 {count} 条signature为空字符串的数据")
        
        # 确认删除
        confirm = input(f"确认删除这 {count} 条记录吗？(y/N): ")
        if confirm.lower() != 'y':
            logging.info("用户取消删除操作")
            return 0
        
        # 执行删除
        delete_sql = """
        DELETE FROM customer_analysis_results 
        WHERE signature = ''
        """
        cursor.execute(delete_sql)
        connection.commit()
        
        deleted_count = cursor.rowcount
        logging.info(f"成功删除 {deleted_count} 条记录")
        
        cursor.close()
        return deleted_count
        
    except Exception as e:
        connection.rollback()
        logging.error(f"删除数据失败: {e}")
        raise

def main():
    """主函数"""
    logging.info("=" * 50)
    logging.info("客户分析数据清理工具启动")
    logging.info("=" * 50)
    
    try:
        connection = connect_to_database()
        
        try:
            print("\n选择清理方式:")
            print("1. 删除最近2小时内导入的数据")
            print("2. 删除signature为空字符串的数据")
            print("3. 两种方式都尝试")
            
            choice = input("请选择 (1/2/3): ")
            
            total_deleted = 0
            
            if choice in ['1', '3']:
                deleted = cleanup_recent_data(connection, hours_back=2)
                total_deleted += deleted
            
            if choice in ['2', '3']:
                deleted = cleanup_by_signature(connection)
                total_deleted += deleted
            
            logging.info("=" * 50)
            logging.info(f"清理完成，总共删除 {total_deleted} 条记录")
            logging.info("=" * 50)
            
        finally:
            connection.close()
            logging.info("数据库连接已关闭")
            
    except Exception as e:
        logging.error(f"清理过程发生错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
