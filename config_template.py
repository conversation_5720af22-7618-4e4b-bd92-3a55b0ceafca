#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置模板文件
复制此文件为 config.py 并修改相应的配置信息
"""

# MySQL数据库配置
DB_CONFIG = {
    'host': '**********',          # 数据库主机地址
    'port': 3306,                # 数据库端口
    'user': 'root',     # 数据库用户名
    'password': 'LianDodmx_03', # 数据库密码
    'database': 'dyredbook',     # 数据库名称
    'charset': 'utf8mb4'         # 字符集
}

# CSV文件配置
CSV_CONFIG = {
    'file_path': 'liandong_search_results_1753841820642.csv',  # CSV文件路径
    'encoding': 'utf-8'  # 文件编码
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',              # 日志级别: DEBUG, INFO, WARNING, ERROR
    'file_name': 'import_log.log', # 日志文件名
    'max_file_size': 10 * 1024 * 1024,  # 最大日志文件大小 (10MB)
    'backup_count': 5             # 保留的日志文件数量
}

# 数据处理配置
DATA_CONFIG = {
    'batch_size': 1000,           # 批量插入的记录数
    'skip_duplicates': True,      # 是否跳过重复记录
    'validate_data': True         # 是否验证数据格式
}
