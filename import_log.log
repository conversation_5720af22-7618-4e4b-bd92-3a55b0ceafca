2025-07-30 10:51:26,154 - INFO - ============================================================
2025-07-30 10:51:26,154 - INFO - 抖音视频数据导入工具启动
2025-07-30 10:51:26,154 - INFO - 启动时间: 2025-07-30 10:51:26.154480
2025-07-30 10:51:26,154 - INFO - ============================================================
2025-07-30 10:51:26,154 - INFO - 找到CSV文件: liandong_search_results_1753841820642.csv
2025-07-30 10:51:26,154 - INFO - 测试数据库连接...
2025-07-30 10:51:26,351 - INFO - 数据库连接测试成功
2025-07-30 10:51:26,351 - INFO - 正在读取CSV文件...
2025-07-30 10:51:26,360 - INFO - 成功读取CSV文件，共 1098 条记录
2025-07-30 10:51:26,360 - INFO - CSV文件结构验证通过
2025-07-30 10:51:26,360 - INFO - 正在清洗和转换数据...
2025-07-30 10:51:26,360 - INFO - 开始数据清洗和转换...
2025-07-30 10:51:26,363 - INFO - 删除video_id为空的记录后，剩余 1098 条记录
2025-07-30 10:51:26,365 - WARNING - 字段 like_count 有 186 个异常值被转换为0
2025-07-30 10:51:26,366 - WARNING - 字段 comment_count 有 309 个异常值被转换为0
2025-07-30 10:51:26,366 - WARNING - 字段 share_count 有 506 个异常值被转换为0
2025-07-30 10:51:26,367 - WARNING - 字段 play_count 有 1098 个异常值被转换为0
2025-07-30 10:51:26,367 - INFO - 转换发布时间格式...
2025-07-30 10:51:26,376 - INFO - 数据清洗完成，原始记录: 1098，最终有效记录: 1098
2025-07-30 10:51:26,376 - INFO - === 数据统计摘要 ===
2025-07-30 10:51:26,376 - INFO - 总记录数: 1098
2025-07-30 10:51:26,376 - INFO - 唯一视频ID数: 1098
2025-07-30 10:51:26,376 - INFO - 搜索话题数: 1
2025-07-30 10:51:26,376 - INFO - 作者数量: 807
2025-07-30 10:51:26,379 - INFO - 数值字段统计:
2025-07-30 10:51:26,380 - INFO -   like_count: 平均=2166.8, 最大=848006
2025-07-30 10:51:26,380 - INFO -   comment_count: 平均=169.8, 最大=76283
2025-07-30 10:51:26,380 - INFO -   share_count: 平均=1844.3, 最大=1191515
2025-07-30 10:51:26,380 - INFO -   play_count: 平均=0.0, 最大=0
2025-07-30 10:51:26,380 - INFO - 发布时间范围: 2020-08-24 11:49:19 到 2025-07-30 09:36:23
2025-07-30 10:51:26,380 - INFO - 正在连接数据库...
2025-07-30 10:51:26,546 - INFO - 数据库连接成功
2025-07-30 10:51:26,584 - INFO - 目标表 douyin_video_data 存在
2025-07-30 10:51:26,584 - INFO - 正在插入数据到数据库...
2025-07-30 10:51:26,585 - INFO - 开始批量插入数据，总记录数: 1098，批次大小: 1000
2025-07-30 10:51:26,649 - ERROR - 批次 1 插入失败: nan can not be used with MySQL
2025-07-30 10:51:26,649 - INFO - 尝试逐条插入以识别问题记录...
2025-07-30 10:51:26,686 - ERROR - 记录 1 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:26,686 - ERROR - 问题数据: video_id=7532682777688247612
2025-07-30 10:51:26,755 - ERROR - 记录 2 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:26,755 - ERROR - 问题数据: video_id=7532682070359952682
2025-07-30 10:51:26,824 - ERROR - 记录 3 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:26,824 - ERROR - 问题数据: video_id=7532679773824781583
2025-07-30 10:51:26,886 - ERROR - 记录 4 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:26,886 - ERROR - 问题数据: video_id=7532679587533622588
2025-07-30 10:51:26,955 - ERROR - 记录 5 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:26,955 - ERROR - 问题数据: video_id=7529424064197217572
2025-07-30 10:51:27,026 - ERROR - 记录 6 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,026 - ERROR - 问题数据: video_id=7532679453994978587
2025-07-30 10:51:27,091 - ERROR - 记录 7 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,091 - ERROR - 问题数据: video_id=7532676236524113210
2025-07-30 10:51:27,162 - ERROR - 记录 8 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,162 - ERROR - 问题数据: video_id=7532675745543130425
2025-07-30 10:51:27,226 - ERROR - 记录 9 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,227 - ERROR - 问题数据: video_id=7532672554461449472
2025-07-30 10:51:27,294 - ERROR - 记录 10 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,294 - ERROR - 问题数据: video_id=7532670865739124011
2025-07-30 10:51:27,363 - ERROR - 记录 11 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,363 - ERROR - 问题数据: video_id=7532665560157457700
2025-07-30 10:51:27,420 - ERROR - 记录 12 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,420 - ERROR - 问题数据: video_id=7532665671210667324
2025-07-30 10:51:27,485 - ERROR - 记录 13 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,485 - ERROR - 问题数据: video_id=7532663800969891114
2025-07-30 10:51:27,553 - ERROR - 记录 14 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,553 - ERROR - 问题数据: video_id=7532663334634573090
2025-07-30 10:51:27,621 - ERROR - 记录 15 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,621 - ERROR - 问题数据: video_id=7532662211358002459
2025-07-30 10:51:27,693 - ERROR - 记录 16 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,694 - ERROR - 问题数据: video_id=7529602621473099017
2025-07-30 10:51:27,763 - ERROR - 记录 17 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,764 - ERROR - 问题数据: video_id=7532653031121915136
2025-07-30 10:51:27,834 - ERROR - 记录 18 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,835 - ERROR - 问题数据: video_id=7532330470881643817
2025-07-30 10:51:27,895 - ERROR - 记录 19 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,895 - ERROR - 问题数据: video_id=7532644870633147667
2025-07-30 10:51:27,963 - ERROR - 记录 20 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:27,964 - ERROR - 问题数据: video_id=7532641021836217634
2025-07-30 10:51:28,034 - ERROR - 记录 21 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,034 - ERROR - 问题数据: video_id=7532627579363233081
2025-07-30 10:51:28,096 - ERROR - 记录 22 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,096 - ERROR - 问题数据: video_id=7532621299068702009
2025-07-30 10:51:28,166 - ERROR - 记录 23 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,166 - ERROR - 问题数据: video_id=7532432140903009575
2025-07-30 10:51:28,238 - ERROR - 记录 24 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,238 - ERROR - 问题数据: video_id=7532619204836822318
2025-07-30 10:51:28,305 - ERROR - 记录 25 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,305 - ERROR - 问题数据: video_id=7532071265331399936
2025-07-30 10:51:28,374 - ERROR - 记录 26 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,374 - ERROR - 问题数据: video_id=7532345844075400467
2025-07-30 10:51:28,445 - ERROR - 记录 27 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,445 - ERROR - 问题数据: video_id=7529942541844745487
2025-07-30 10:51:28,526 - ERROR - 记录 28 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,526 - ERROR - 问题数据: video_id=7531202909053275427
2025-07-30 10:51:28,606 - ERROR - 记录 29 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,606 - ERROR - 问题数据: video_id=7527734160438480128
2025-07-30 10:51:28,673 - ERROR - 记录 30 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,674 - ERROR - 问题数据: video_id=7532552991027596585
2025-07-30 10:51:28,744 - ERROR - 记录 31 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,744 - ERROR - 问题数据: video_id=7532539106590346555
2025-07-30 10:51:28,808 - ERROR - 记录 32 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,809 - ERROR - 问题数据: video_id=7527724548679126306
2025-07-30 10:51:28,876 - ERROR - 记录 33 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,876 - ERROR - 问题数据: video_id=7532531049655668014
2025-07-30 10:51:28,948 - ERROR - 记录 34 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:28,948 - ERROR - 问题数据: video_id=7532522265892965658
2025-07-30 10:51:29,022 - ERROR - 记录 35 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,022 - ERROR - 问题数据: video_id=7532518548950846747
2025-07-30 10:51:29,084 - ERROR - 记录 36 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,084 - ERROR - 问题数据: video_id=7530646798357040422
2025-07-30 10:51:29,153 - ERROR - 记录 37 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,153 - ERROR - 问题数据: video_id=7532515225309285642
2025-07-30 10:51:29,219 - ERROR - 记录 38 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,219 - ERROR - 问题数据: video_id=7532513343917296936
2025-07-30 10:51:29,285 - ERROR - 记录 39 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,285 - ERROR - 问题数据: video_id=7532504144003878170
2025-07-30 10:51:29,355 - ERROR - 记录 40 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,355 - ERROR - 问题数据: video_id=7532503963472760123
2025-07-30 10:51:29,426 - ERROR - 记录 41 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,426 - ERROR - 问题数据: video_id=7527488648049200425
2025-07-30 10:51:29,489 - ERROR - 记录 42 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,489 - ERROR - 问题数据: video_id=7532050367287381306
2025-07-30 10:51:29,556 - ERROR - 记录 43 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,556 - ERROR - 问题数据: video_id=7532495796420332842
2025-07-30 10:51:29,588 - ERROR - 记录 44 插入失败: nan can not be used with MySQL
2025-07-30 10:51:29,588 - ERROR - 问题数据: video_id=7532483458968866074
2025-07-30 10:51:29,654 - ERROR - 记录 45 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,654 - ERROR - 问题数据: video_id=7532482530063207719
2025-07-30 10:51:29,716 - ERROR - 记录 46 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,716 - ERROR - 问题数据: video_id=7532474382054051132
2025-07-30 10:51:29,786 - ERROR - 记录 47 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,786 - ERROR - 问题数据: video_id=7532472925379710267
2025-07-30 10:51:29,856 - ERROR - 记录 48 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,856 - ERROR - 问题数据: video_id=7530427156980108578
2025-07-30 10:51:29,922 - ERROR - 记录 49 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,923 - ERROR - 问题数据: video_id=7532471792348597563
2025-07-30 10:51:29,981 - ERROR - 记录 50 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:29,981 - ERROR - 问题数据: video_id=7532467376110062911
2025-07-30 10:51:30,044 - ERROR - 记录 51 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,044 - ERROR - 问题数据: video_id=7532467419022740755
2025-07-30 10:51:30,105 - ERROR - 记录 52 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,105 - ERROR - 问题数据: video_id=7532466932805700873
2025-07-30 10:51:30,173 - ERROR - 记录 53 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,174 - ERROR - 问题数据: video_id=7532465344376704256
2025-07-30 10:51:30,236 - ERROR - 记录 54 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,237 - ERROR - 问题数据: video_id=7532462816923012411
2025-07-30 10:51:30,306 - ERROR - 记录 55 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,307 - ERROR - 问题数据: video_id=7532461497780800810
2025-07-30 10:51:30,382 - ERROR - 记录 56 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,382 - ERROR - 问题数据: video_id=7532460965766696227
2025-07-30 10:51:30,450 - ERROR - 记录 57 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,451 - ERROR - 问题数据: video_id=7532407642724126006
2025-07-30 10:51:30,518 - ERROR - 记录 58 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,518 - ERROR - 问题数据: video_id=7532456806778441019
2025-07-30 10:51:30,582 - ERROR - 记录 59 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,583 - ERROR - 问题数据: video_id=7532456531515362599
2025-07-30 10:51:30,650 - ERROR - 记录 60 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,650 - ERROR - 问题数据: video_id=7532455999598071098
2025-07-30 10:51:30,681 - ERROR - 记录 61 插入失败: nan can not be used with MySQL
2025-07-30 10:51:30,681 - ERROR - 问题数据: video_id=7532451967483432251
2025-07-30 10:51:30,709 - ERROR - 记录 62 插入失败: nan can not be used with MySQL
2025-07-30 10:51:30,709 - ERROR - 问题数据: video_id=7532449513069186364
2025-07-30 10:51:30,776 - ERROR - 记录 63 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,776 - ERROR - 问题数据: video_id=7532347913163279631
2025-07-30 10:51:30,846 - ERROR - 记录 64 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,846 - ERROR - 问题数据: video_id=7532448169242774820
2025-07-30 10:51:30,921 - ERROR - 记录 65 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,921 - ERROR - 问题数据: video_id=7532447373675547945
2025-07-30 10:51:30,993 - ERROR - 记录 66 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:30,994 - ERROR - 问题数据: video_id=7532446171106446652
2025-07-30 10:51:31,060 - ERROR - 记录 67 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,061 - ERROR - 问题数据: video_id=7532445882488753465
2025-07-30 10:51:31,126 - ERROR - 记录 68 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,126 - ERROR - 问题数据: video_id=7532444592337571129
2025-07-30 10:51:31,194 - ERROR - 记录 69 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,195 - ERROR - 问题数据: video_id=7532443878034902299
2025-07-30 10:51:31,264 - ERROR - 记录 70 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,264 - ERROR - 问题数据: video_id=7532443521937083691
2025-07-30 10:51:31,331 - ERROR - 记录 71 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,332 - ERROR - 问题数据: video_id=7532442297393319228
2025-07-30 10:51:31,403 - ERROR - 记录 72 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,403 - ERROR - 问题数据: video_id=7532440149397982500
2025-07-30 10:51:31,472 - ERROR - 记录 73 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,472 - ERROR - 问题数据: video_id=7532437857828769051
2025-07-30 10:51:31,540 - ERROR - 记录 74 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,540 - ERROR - 问题数据: video_id=7532434212343876891
2025-07-30 10:51:31,609 - ERROR - 记录 75 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,609 - ERROR - 问题数据: video_id=7532056555223518464
2025-07-30 10:51:31,676 - ERROR - 记录 76 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,676 - ERROR - 问题数据: video_id=7532331594393947435
2025-07-30 10:51:31,743 - ERROR - 记录 77 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,743 - ERROR - 问题数据: video_id=7532431572952190251
2025-07-30 10:51:31,812 - ERROR - 记录 78 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,813 - ERROR - 问题数据: video_id=7532427535649344811
2025-07-30 10:51:31,883 - ERROR - 记录 79 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,883 - ERROR - 问题数据: video_id=7532425235413617978
2025-07-30 10:51:31,946 - ERROR - 记录 80 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:31,946 - ERROR - 问题数据: video_id=7532423686021664057
2025-07-30 10:51:31,978 - ERROR - 记录 81 插入失败: nan can not be used with MySQL
2025-07-30 10:51:31,979 - ERROR - 问题数据: video_id=7532423433964342586
2025-07-30 10:51:32,048 - ERROR - 记录 82 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:32,048 - ERROR - 问题数据: video_id=7532422035004247323
2025-07-30 10:51:32,116 - ERROR - 记录 83 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:32,117 - ERROR - 问题数据: video_id=7532020115480579362
2025-07-30 10:51:32,181 - ERROR - 记录 84 插入失败: (1048, "Column 'is_related' cannot be null")
2025-07-30 10:51:32,181 - ERROR - 问题数据: video_id=7532419926850981146
2025-07-30 10:53:46,611 - INFO - ============================================================
2025-07-30 10:53:46,611 - INFO - 抖音视频数据导入工具启动
2025-07-30 10:53:46,611 - INFO - 启动时间: 2025-07-30 10:53:46.611735
2025-07-30 10:53:46,611 - INFO - ============================================================
2025-07-30 10:53:46,612 - INFO - 找到CSV文件: liandong_search_results_1753841820642.csv
2025-07-30 10:53:46,612 - INFO - 测试数据库连接...
2025-07-30 10:53:46,826 - INFO - 数据库连接测试成功
2025-07-30 10:53:46,827 - INFO - 正在读取CSV文件...
2025-07-30 10:53:46,834 - INFO - 成功读取CSV文件，共 1098 条记录
2025-07-30 10:53:46,834 - INFO - CSV文件结构验证通过
2025-07-30 10:53:46,834 - INFO - 正在清洗和转换数据...
2025-07-30 10:53:46,834 - INFO - 开始数据清洗和转换...
2025-07-30 10:53:46,835 - INFO - 删除video_id为空的记录后，剩余 1098 条记录
2025-07-30 10:53:46,836 - WARNING - 字段 like_count 有 186 个异常值被转换为0
2025-07-30 10:53:46,836 - WARNING - 字段 comment_count 有 309 个异常值被转换为0
2025-07-30 10:53:46,837 - WARNING - 字段 share_count 有 506 个异常值被转换为0
2025-07-30 10:53:46,837 - WARNING - 字段 play_count 有 1098 个异常值被转换为0
2025-07-30 10:53:46,837 - INFO - 转换发布时间格式...
2025-07-30 10:53:46,841 - INFO - 数据清洗完成，原始记录: 1098，最终有效记录: 1098
2025-07-30 10:53:46,841 - INFO - === 数据统计摘要 ===
2025-07-30 10:53:46,841 - INFO - 总记录数: 1098
2025-07-30 10:53:46,841 - INFO - 唯一视频ID数: 1098
2025-07-30 10:53:46,841 - INFO - 搜索话题数: 1
2025-07-30 10:53:46,841 - INFO - 作者数量: 807
2025-07-30 10:53:46,844 - INFO - 数值字段统计:
2025-07-30 10:53:46,844 - INFO -   like_count: 平均=2166.8, 最大=848006
2025-07-30 10:53:46,844 - INFO -   comment_count: 平均=169.8, 最大=76283
2025-07-30 10:53:46,844 - INFO -   share_count: 平均=1844.3, 最大=1191515
2025-07-30 10:53:46,844 - INFO -   play_count: 平均=0.0, 最大=0
2025-07-30 10:53:46,844 - INFO - 发布时间范围: 2020-08-24 11:49:19 到 2025-07-30 09:36:23
2025-07-30 10:53:46,844 - INFO - 正在连接数据库...
2025-07-30 10:53:46,990 - INFO - 数据库连接成功
2025-07-30 10:53:47,029 - INFO - 目标表 douyin_video_data 存在
2025-07-30 10:53:47,030 - INFO - 正在插入数据到数据库...
2025-07-30 10:53:47,030 - INFO - 开始批量插入数据，总记录数: 1098，批次大小: 1000
2025-07-30 10:53:47,095 - ERROR - 批次 1 插入失败: nan can not be used with MySQL
2025-07-30 10:53:47,095 - INFO - 尝试逐条插入以识别问题记录...
2025-07-30 10:53:50,075 - ERROR - 记录 44 插入失败: nan can not be used with MySQL
2025-07-30 10:53:50,076 - ERROR - 问题数据: video_id=7532483458968866074
2025-07-30 10:53:51,203 - ERROR - 记录 61 插入失败: nan can not be used with MySQL
2025-07-30 10:53:51,203 - ERROR - 问题数据: video_id=7532451967483432251
2025-07-30 10:53:51,232 - ERROR - 记录 62 插入失败: nan can not be used with MySQL
2025-07-30 10:53:51,232 - ERROR - 问题数据: video_id=7532449513069186364
2025-07-30 10:53:52,357 - ERROR - 记录 81 插入失败: nan can not be used with MySQL
2025-07-30 10:53:52,357 - ERROR - 问题数据: video_id=7532423433964342586
2025-07-30 10:54:48,714 - INFO - ============================================================
2025-07-30 10:54:48,714 - INFO - 抖音视频数据导入工具启动
2025-07-30 10:54:48,714 - INFO - 启动时间: 2025-07-30 10:54:48.714528
2025-07-30 10:54:48,714 - INFO - ============================================================
2025-07-30 10:54:48,714 - INFO - 找到CSV文件: liandong_search_results_1753841820642.csv
2025-07-30 10:54:48,714 - INFO - 测试数据库连接...
2025-07-30 10:54:48,951 - INFO - 数据库连接测试成功
2025-07-30 10:54:48,951 - INFO - 正在读取CSV文件...
2025-07-30 10:54:48,960 - INFO - 成功读取CSV文件，共 1098 条记录
2025-07-30 10:54:48,961 - INFO - CSV文件结构验证通过
2025-07-30 10:54:48,961 - INFO - 正在清洗和转换数据...
2025-07-30 10:54:48,961 - INFO - 开始数据清洗和转换...
2025-07-30 10:54:48,963 - INFO - 删除video_id为空的记录后，剩余 1098 条记录
2025-07-30 10:54:48,966 - WARNING - 字段 like_count 有 186 个异常值被转换为0
2025-07-30 10:54:48,967 - WARNING - 字段 comment_count 有 309 个异常值被转换为0
2025-07-30 10:54:48,968 - WARNING - 字段 share_count 有 506 个异常值被转换为0
2025-07-30 10:54:48,969 - WARNING - 字段 play_count 有 1098 个异常值被转换为0
2025-07-30 10:54:48,970 - ERROR - 数据清洗失败: Must specify a fill 'value' or 'method'.
2025-07-30 10:54:48,970 - ERROR - 数据导入过程中发生错误: Must specify a fill 'value' or 'method'.
2025-07-30 10:54:48,970 - ERROR - 详细错误信息:
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/reachradar_data/import_douyin_data.py", line 446, in main
    df_cleaned = clean_and_convert_data(df)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/reachradar_data/import_douyin_data.py", line 183, in clean_and_convert_data
    df = df.fillna({
         ^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/videogenerate/lib/python3.12/site-packages/pandas/core/generic.py", line 7403, in fillna
    res_k = result[k].fillna(v, limit=limit, downcast=downcast_k)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/videogenerate/lib/python3.12/site-packages/pandas/core/generic.py", line 7312, in fillna
    value, method = validate_fillna_kwargs(value, method)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/envs/videogenerate/lib/python3.12/site-packages/pandas/util/_validators.py", line 293, in validate_fillna_kwargs
    raise ValueError("Must specify a fill 'value' or 'method'.")
ValueError: Must specify a fill 'value' or 'method'.
2025-07-30 10:55:40,198 - INFO - ============================================================
2025-07-30 10:55:40,198 - INFO - 抖音视频数据导入工具启动
2025-07-30 10:55:40,198 - INFO - 启动时间: 2025-07-30 10:55:40.198252
2025-07-30 10:55:40,198 - INFO - ============================================================
2025-07-30 10:55:40,198 - INFO - 找到CSV文件: liandong_search_results_1753841820642.csv
2025-07-30 10:55:40,198 - INFO - 测试数据库连接...
2025-07-30 10:55:40,381 - INFO - 数据库连接测试成功
2025-07-30 10:55:40,381 - INFO - 正在读取CSV文件...
2025-07-30 10:55:40,392 - INFO - 成功读取CSV文件，共 1098 条记录
2025-07-30 10:55:40,392 - INFO - CSV文件结构验证通过
2025-07-30 10:55:40,392 - INFO - 正在清洗和转换数据...
2025-07-30 10:55:40,392 - INFO - 开始数据清洗和转换...
2025-07-30 10:55:40,393 - INFO - 删除video_id为空的记录后，剩余 1098 条记录
2025-07-30 10:55:40,395 - WARNING - 字段 like_count 有 186 个异常值被转换为0
2025-07-30 10:55:40,396 - WARNING - 字段 comment_count 有 309 个异常值被转换为0
2025-07-30 10:55:40,397 - WARNING - 字段 share_count 有 506 个异常值被转换为0
2025-07-30 10:55:40,398 - WARNING - 字段 play_count 有 1098 个异常值被转换为0
2025-07-30 10:55:40,399 - INFO - 转换发布时间格式...
2025-07-30 10:55:40,403 - INFO - 数据清洗完成，原始记录: 1098，最终有效记录: 1098
2025-07-30 10:55:40,403 - INFO - === 数据统计摘要 ===
2025-07-30 10:55:40,403 - INFO - 总记录数: 1098
2025-07-30 10:55:40,403 - INFO - 唯一视频ID数: 1098
2025-07-30 10:55:40,404 - INFO - 搜索话题数: 1
2025-07-30 10:55:40,404 - INFO - 作者数量: 807
2025-07-30 10:55:40,407 - INFO - 数值字段统计:
2025-07-30 10:55:40,407 - INFO -   like_count: 平均=2166.8, 最大=848006
2025-07-30 10:55:40,407 - INFO -   comment_count: 平均=169.8, 最大=76283
2025-07-30 10:55:40,407 - INFO -   share_count: 平均=1844.3, 最大=1191515
2025-07-30 10:55:40,408 - INFO -   play_count: 平均=0.0, 最大=0
2025-07-30 10:55:40,408 - INFO - 发布时间范围: 2020-08-24 11:49:19 到 2025-07-30 09:36:23
2025-07-30 10:55:40,408 - INFO - 正在连接数据库...
2025-07-30 10:55:40,561 - INFO - 数据库连接成功
2025-07-30 10:55:40,593 - INFO - 目标表 douyin_video_data 存在
2025-07-30 10:55:40,593 - INFO - 正在插入数据到数据库...
2025-07-30 10:55:40,594 - INFO - 开始批量插入数据，总记录数: 1098，批次大小: 1000
2025-07-30 10:55:40,936 - INFO - 批次 1: 成功插入 907 条记录 (进度: 1000/1098)
2025-07-30 10:55:41,016 - INFO - 批次 2: 成功插入 100 条记录 (进度: 1098/1098)
2025-07-30 10:55:41,016 - INFO - 数据插入完成，总共成功插入 1007 条记录
2025-07-30 10:55:41,017 - INFO - ============================================================
2025-07-30 10:55:41,017 - INFO - 数据导入完成！
2025-07-30 10:55:41,017 - INFO - 处理记录数: 1098 -> 1098
2025-07-30 10:55:41,017 - INFO - 成功插入: 1007 条记录
2025-07-30 10:55:41,017 - INFO - 总耗时: 0:00:00.818832
2025-07-30 10:55:41,017 - INFO - 完成时间: 2025-07-30 10:55:41.017084
2025-07-30 10:55:41,017 - INFO - ============================================================
2025-07-30 10:55:41,017 - INFO - 数据库连接已关闭
2025-07-30 13:20:45,714 - INFO - ============================================================
2025-07-30 13:20:45,714 - INFO - 客户分析数据导入工具启动
2025-07-30 13:20:45,714 - INFO - 启动时间: 2025-07-30 13:20:45.714276
2025-07-30 13:20:45,714 - INFO - ============================================================
2025-07-30 13:20:45,714 - INFO - 找到CSV文件: customer_analysis_1753851782825.csv
2025-07-30 13:20:45,714 - INFO - 测试数据库连接...
2025-07-30 13:20:45,908 - INFO - 数据库连接测试成功
2025-07-30 13:20:45,908 - INFO - 正在读取CSV文件...
2025-07-30 13:20:45,916 - INFO - 成功读取CSV文件，共 1011 条记录
2025-07-30 13:20:45,916 - INFO - CSV文件结构验证通过
2025-07-30 13:20:45,916 - INFO - 正在清洗和转换数据...
2025-07-30 13:20:45,916 - INFO - 开始数据清洗和转换...
2025-07-30 13:20:45,919 - INFO - 删除关键字段为空的记录后，剩余 1011 条记录
2025-07-30 13:20:45,922 - INFO - 转换最新评论时间格式...
2025-07-30 13:20:45,924 - INFO - 数据清洗完成，原始记录: 1011，最终有效记录: 1011
2025-07-30 13:20:45,924 - INFO - === 客户分析数据统计摘要 ===
2025-07-30 13:20:45,924 - INFO - 总记录数: 1011
2025-07-30 13:20:45,925 - INFO - 唯一用户数: 1009
2025-07-30 13:20:45,925 - INFO - 唯一抖音号数: 1011
2025-07-30 13:20:45,925 - INFO - 意向类型分布:
2025-07-30 13:20:45,925 - INFO -   租赁: 488 条
2025-07-30 13:20:45,925 - INFO -   待定: 382 条
2025-07-30 13:20:45,925 - INFO -   购买: 141 条
2025-07-30 13:20:45,927 - INFO - 置信度统计: 平均=0.695, 最小=0.600, 最大=0.950
2025-07-30 13:20:45,927 - INFO - 评论数量统计: 平均=1.1, 最大=11
2025-07-30 13:20:45,927 - INFO - 正在连接数据库...
2025-07-30 13:20:46,086 - INFO - 数据库连接成功
2025-07-30 13:20:46,131 - INFO - 目标表 customer_analysis_results 存在
2025-07-30 13:20:46,131 - INFO - 正在插入数据到数据库...
2025-07-30 13:20:46,131 - INFO - 开始批量插入数据，总记录数: 1011，批次大小: 1000
2025-07-30 13:20:46,513 - ERROR - 批次 1 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:46,514 - INFO - 尝试逐条插入以识别问题记录...
2025-07-30 13:20:46,550 - ERROR - 记录 1 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:46,550 - ERROR - 问题数据: user_nickname=彦彦的小厨房, douyin_id=2051671356
2025-07-30 13:20:46,615 - ERROR - 记录 2 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:46,615 - ERROR - 问题数据: user_nickname=大叔说房, douyin_id=74963820476
2025-07-30 13:20:46,681 - ERROR - 记录 3 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:46,681 - ERROR - 问题数据: user_nickname=吕先生, douyin_id=977095739
2025-07-30 13:20:46,748 - ERROR - 记录 4 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:46,749 - ERROR - 问题数据: user_nickname=ygggggggggg, douyin_id=1093358178
2025-07-30 13:20:46,813 - ERROR - 记录 5 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:46,813 - ERROR - 问题数据: user_nickname=褪色的刺青, douyin_id=43389397636
2025-07-30 13:20:46,877 - ERROR - 记录 6 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:46,877 - ERROR - 问题数据: user_nickname=高大伟, douyin_id=daweigetongxue
2025-07-30 13:20:46,943 - ERROR - 记录 7 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:46,943 - ERROR - 问题数据: user_nickname=，, douyin_id=62289881309
2025-07-30 13:20:47,011 - ERROR - 记录 8 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,011 - ERROR - 问题数据: user_nickname=吉林省嘉宝广告有限公司, douyin_id=LiJiaBao88888888
2025-07-30 13:20:47,077 - ERROR - 记录 9 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,077 - ERROR - 问题数据: user_nickname=@鸿雁@, douyin_id=65717211436
2025-07-30 13:20:47,141 - ERROR - 记录 10 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,141 - ERROR - 问题数据: user_nickname=小Y想上岸, douyin_id=52529074391
2025-07-30 13:20:47,200 - ERROR - 记录 11 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,200 - ERROR - 问题数据: user_nickname=张温柔💕, douyin_id=Zsy929.
2025-07-30 13:20:47,263 - ERROR - 记录 12 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,264 - ERROR - 问题数据: user_nickname=大帥, douyin_id=117442134
2025-07-30 13:20:47,331 - ERROR - 记录 13 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,331 - ERROR - 问题数据: user_nickname=减一, douyin_id=dy9umz1rmsfx
2025-07-30 13:20:47,393 - ERROR - 记录 14 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,393 - ERROR - 问题数据: user_nickname=晓辉, douyin_id=dyr9skhpvs4c
2025-07-30 13:20:47,460 - ERROR - 记录 15 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,460 - ERROR - 问题数据: user_nickname=磊子哥海参批发大卖场, douyin_id=Y252793648
2025-07-30 13:20:47,529 - ERROR - 记录 16 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,529 - ERROR - 问题数据: user_nickname=男人中的男人, douyin_id=1893102169
2025-07-30 13:20:47,594 - ERROR - 记录 17 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,594 - ERROR - 问题数据: user_nickname=Cookies家•门业, douyin_id=583619318
2025-07-30 13:20:47,660 - ERROR - 记录 18 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,661 - ERROR - 问题数据: user_nickname=牡丹江森保山货, douyin_id=xzj4519
2025-07-30 13:20:47,732 - ERROR - 记录 19 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,732 - ERROR - 问题数据: user_nickname=Yangxiaobao, douyin_id=Y13841140902
2025-07-30 13:20:47,781 - ERROR - 记录 20 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,781 - ERROR - 问题数据: user_nickname=潘潘摇花手, douyin_id=1582874144.
2025-07-30 13:20:47,839 - ERROR - 记录 21 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,840 - ERROR - 问题数据: user_nickname=顾奈., douyin_id=zxcnzs520
2025-07-30 13:20:47,906 - ERROR - 记录 22 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,906 - ERROR - 问题数据: user_nickname=时孤荼靡., douyin_id=89103929088
2025-07-30 13:20:47,977 - ERROR - 记录 23 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:47,977 - ERROR - 问题数据: user_nickname=高艳, douyin_id=g13624401963
2025-07-30 13:20:48,039 - ERROR - 记录 24 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,039 - ERROR - 问题数据: user_nickname=怀瑾, douyin_id=193503588
2025-07-30 13:20:48,097 - ERROR - 记录 25 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,097 - ERROR - 问题数据: user_nickname=混迹, douyin_id=1043627575
2025-07-30 13:20:48,161 - ERROR - 记录 26 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,161 - ERROR - 问题数据: user_nickname=猜心游戏, douyin_id=88189101067
2025-07-30 13:20:48,232 - ERROR - 记录 27 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,232 - ERROR - 问题数据: user_nickname=回报社会修行自我, douyin_id=23371786456
2025-07-30 13:20:48,299 - ERROR - 记录 28 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,299 - ERROR - 问题数据: user_nickname=合肥企多帮智能科技有限公司, douyin_id=dyhdq16ifta6
2025-07-30 13:20:48,364 - ERROR - 记录 29 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,364 - ERROR - 问题数据: user_nickname=风生水起靠自己, douyin_id=1799147426
2025-07-30 13:20:48,429 - ERROR - 记录 30 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,429 - ERROR - 问题数据: user_nickname=、薄荷梦, douyin_id=A7L81G
2025-07-30 13:20:48,494 - ERROR - 记录 31 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,494 - ERROR - 问题数据: user_nickname=果姨, douyin_id=ll240706
2025-07-30 13:20:48,561 - ERROR - 记录 32 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,561 - ERROR - 问题数据: user_nickname=六静山人, douyin_id=80148786000
2025-07-30 13:20:48,627 - ERROR - 记录 33 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,627 - ERROR - 问题数据: user_nickname=▛▖▜▖▗▝五, douyin_id=62105554860
2025-07-30 13:20:48,691 - ERROR - 记录 34 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,692 - ERROR - 问题数据: user_nickname=经典语录−恒少, douyin_id=Hengshao521
2025-07-30 13:20:48,757 - ERROR - 记录 35 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,757 - ERROR - 问题数据: user_nickname=来个汉堡🍔, douyin_id=lwl913
2025-07-30 13:20:48,816 - ERROR - 记录 36 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,817 - ERROR - 问题数据: user_nickname=LZ·, douyin_id=lyzy.
2025-07-30 13:20:48,880 - ERROR - 记录 37 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,880 - ERROR - 问题数据: user_nickname=长春守村人, douyin_id=57164244177
2025-07-30 13:20:48,944 - ERROR - 记录 38 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:48,944 - ERROR - 问题数据: user_nickname=冰城🇨🇳卓先生（婉儿爸爸）, douyin_id=bingchengcangbao
2025-07-30 13:20:49,010 - ERROR - 记录 39 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:49,010 - ERROR - 问题数据: user_nickname=老李, douyin_id=98822016110
2025-07-30 13:20:49,076 - ERROR - 记录 40 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:49,076 - ERROR - 问题数据: user_nickname=冰城✨军哥哥✨, douyin_id=90826058143
2025-07-30 13:20:49,146 - ERROR - 记录 41 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:49,146 - ERROR - 问题数据: user_nickname=发电机组 高价 回收   出租  买卖, douyin_id=34348466713
2025-07-30 13:20:49,212 - ERROR - 记录 42 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:49,212 - ERROR - 问题数据: user_nickname=LMX, douyin_id=39846036989
2025-07-30 13:20:49,279 - ERROR - 记录 43 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:49,280 - ERROR - 问题数据: user_nickname=双洋房产, douyin_id=39693128071
2025-07-30 13:20:49,347 - ERROR - 记录 44 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:49,347 - ERROR - 问题数据: user_nickname=为你停留, douyin_id=dyt2yvw01vay
2025-07-30 13:20:49,411 - ERROR - 记录 45 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:49,411 - ERROR - 问题数据: user_nickname=duck不必, douyin_id=93264106506
2025-07-30 13:20:49,476 - ERROR - 记录 46 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:49,476 - ERROR - 问题数据: user_nickname=金小欠逛吃沈阳, douyin_id=jinxiaoqian000
2025-07-30 13:20:49,540 - ERROR - 记录 47 插入失败: (1048, "Column 'status' cannot be null")
2025-07-30 13:20:49,540 - ERROR - 问题数据: user_nickname=耍耍三郎, douyin_id=24761495253
