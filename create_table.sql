-- 创建抖音视频数据表
-- 数据库: dyredbook
-- 表名: douyin_video_data

USE dyredbook;

-- 如果表已存在则删除（谨慎使用）
-- DROP TABLE IF EXISTS douyin_video_data;

-- 创建表结构
CREATE TABLE IF NOT EXISTS `douyin_video_data` (
  `search_topic` varchar(255) DEFAULT NULL COMMENT '搜索话题',
  `video_id` bigint NOT NULL COMMENT '视频ID',
  `video_title_description` text COMMENT '视频标题/描述',
  `author_nickname` varchar(255) DEFAULT NULL COMMENT '作者昵称',
  `author_douyin_id` varchar(255) DEFAULT NULL COMMENT '作者抖音号',
  `like_count` bigint DEFAULT NULL COMMENT '点赞数',
  `comment_count` bigint DEFAULT NULL COMMENT '评论数',
  `share_count` bigint DEFAULT NULL COMMENT '分享数',
  `play_count` bigint DEFAULT NULL COMMENT '播放数',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `video_link` varchar(255) DEFAULT NULL COMMENT '视频链接',
  `is_related` varchar(10) DEFAULT NULL COMMENT '是否相关',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`video_id`),
  KEY `idx_search_topic` (`search_topic`),
  KEY `idx_author_nickname` (`author_nickname`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抖音视频数据表';

-- 显示表结构
DESCRIBE douyin_video_data;

-- 显示创建表的SQL语句
SHOW CREATE TABLE douyin_video_data;
