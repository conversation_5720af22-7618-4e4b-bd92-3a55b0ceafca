#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频数据导入MySQL数据库脚本
将CSV文件中的抖音视频数据导入到MySQL数据库的douyin_video_data表中

使用方法:
1. 修改下面的数据库配置信息
2. 确保CSV文件在正确路径
3. 运行: python import_douyin_data.py

作者: AI Assistant
创建时间: 2025-07-30
"""

import pandas as pd
import pymysql
import logging
from datetime import datetime
import sys
import os
from typing import Optional

# 尝试导入配置文件，如果不存在则使用默认配置
try:
    from config_template import DB_CONFIG, CSV_CONFIG, LOG_CONFIG, DATA_CONFIG
    print("使用自定义配置文件 config.py")
except ImportError:
    print("未找到配置文件，使用默认配置")
    # 默认数据库配置 - 请根据实际情况修改
    DB_CONFIG = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',          # 请修改为实际用户名
        'password': '',          # 请修改为实际密码
        'database': 'dyredbook',
        'charset': 'utf8mb4'
    }

    CSV_CONFIG = {
        'file_path': 'liandong_search_results_1753841820642.csv',
        'encoding': 'utf-8'
    }

    LOG_CONFIG = {
        'level': 'INFO',
        'file_name': 'import_log.log'
    }

    DATA_CONFIG = {
        'batch_size': 1000,
        'skip_duplicates': True
    }

# 配置日志
log_level = getattr(logging, LOG_CONFIG.get('level', 'INFO').upper())
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_CONFIG.get('file_name', 'import_log.log'), encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def test_database_connection() -> bool:
    """
    测试数据库连接

    Returns:
        bool: 连接是否成功
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        cursor.close()
        connection.close()
        logging.info("数据库连接测试成功")
        return True
    except Exception as e:
        logging.error(f"数据库连接测试失败: {e}")
        return False

def connect_to_database() -> pymysql.Connection:
    """
    连接到MySQL数据库

    Returns:
        pymysql.Connection: 数据库连接对象
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logging.info("数据库连接成功")
        return connection
    except pymysql.Error as e:
        logging.error(f"MySQL数据库连接失败: {e}")
        raise
    except Exception as e:
        logging.error(f"数据库连接失败: {e}")
        raise

def validate_csv_structure(df: pd.DataFrame) -> bool:
    """
    验证CSV文件结构是否正确

    Args:
        df (pd.DataFrame): CSV数据

    Returns:
        bool: 结构是否正确
    """
    expected_columns = [
        '搜索话题', '视频ID', '视频标题/描述', '作者昵称', '作者抖音号',
        '点赞数', '评论数', '分享数', '播放数', '发布时间', '视频链接'
    ]

    if list(df.columns) != expected_columns:
        logging.error(f"CSV文件列结构不正确")
        logging.error(f"期望的列: {expected_columns}")
        logging.error(f"实际的列: {list(df.columns)}")
        return False

    logging.info("CSV文件结构验证通过")
    return True

def clean_and_convert_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    清洗和转换CSV数据以匹配数据库表结构

    Args:
        df (pd.DataFrame): 原始CSV数据

    Returns:
        pd.DataFrame: 清洗后的数据
    """
    try:
        logging.info("开始数据清洗和转换...")
        original_count = len(df)

        # 重命名列以匹配数据库字段
        column_mapping = {
            '搜索话题': 'search_topic',
            '视频ID': 'video_id',
            '视频标题/描述': 'video_title_description',
            '作者昵称': 'author_nickname',
            '作者抖音号': 'author_douyin_id',
            '点赞数': 'like_count',
            '评论数': 'comment_count',
            '分享数': 'share_count',
            '播放数': 'play_count',
            '发布时间': 'publish_time',
            '视频链接': 'video_link'
        }

        df = df.rename(columns=column_mapping)

        # 删除video_id为空的记录
        df = df.dropna(subset=['video_id'])
        logging.info(f"删除video_id为空的记录后，剩余 {len(df)} 条记录")

        # 转换video_id为整数类型
        df['video_id'] = pd.to_numeric(df['video_id'], errors='coerce')
        df = df.dropna(subset=['video_id'])
        df['video_id'] = df['video_id'].astype('int64')

        # 处理缺失的作者抖音号
        df['author_douyin_id'] = df['author_douyin_id'].replace(['-', 'nan', ''], None)

        # 转换数值字段，处理异常值
        numeric_columns = ['like_count', 'comment_count', 'share_count', 'play_count']
        for col in numeric_columns:
            # 记录转换前的异常值数量
            before_conversion = df[col].isna().sum()
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
            # 确保非负数
            df[col] = df[col].clip(lower=0).astype('int64')
            after_conversion = (df[col] == 0).sum()
            if after_conversion > before_conversion:
                logging.warning(f"字段 {col} 有 {after_conversion - before_conversion} 个异常值被转换为0")

        # 处理所有可能的NaN值
        df['search_topic'] = df['search_topic'].fillna('')
        df['video_title_description'] = df['video_title_description'].fillna('')
        df['author_nickname'] = df['author_nickname'].fillna('')
        df['video_link'] = df['video_link'].fillna('')
        # author_douyin_id 和 publish_time 保持 NaN 为 None

        # 转换日期时间格式
        logging.info("转换发布时间格式...")
        df['publish_time'] = pd.to_datetime(df['publish_time'], format='%Y/%m/%d %H:%M:%S', errors='coerce')
        invalid_dates = df['publish_time'].isna().sum()
        if invalid_dates > 0:
            logging.warning(f"有 {invalid_dates} 条记录的发布时间格式无效")

        # 确保字符串字段长度不超过限制并处理特殊字符
        string_fields = {
            'search_topic': 255,
            'author_nickname': 255,
            'video_link': 255
        }

        for field, max_length in string_fields.items():
            if field in df.columns:
                # 转换为字符串并截断
                df[field] = df[field].astype(str).str[:max_length]
                # 处理'nan'字符串
                df[field] = df[field].replace('nan', '')

        # 处理author_douyin_id字段
        if 'author_douyin_id' in df.columns:
            df['author_douyin_id'] = df['author_douyin_id'].astype(str).str[:255]
            df['author_douyin_id'] = df['author_douyin_id'].replace(['nan', ''], None)

        # 添加创建时间
        df['create_time'] = datetime.now()

        # 去除重复的video_id
        before_dedup = len(df)
        df = df.drop_duplicates(subset=['video_id'], keep='first')
        after_dedup = len(df)
        if before_dedup != after_dedup:
            logging.info(f"去除重复video_id后，从 {before_dedup} 条记录减少到 {after_dedup} 条记录")

        logging.info(f"数据清洗完成，原始记录: {original_count}，最终有效记录: {len(df)}")
        return df

    except Exception as e:
        logging.error(f"数据清洗失败: {e}")
        raise

def check_table_exists(connection: pymysql.Connection) -> bool:
    """
    检查目标表是否存在

    Args:
        connection (pymysql.Connection): 数据库连接

    Returns:
        bool: 表是否存在
    """
    try:
        cursor = connection.cursor()
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = %s AND table_name = 'douyin_video_data'
        """, (DB_CONFIG['database'],))

        result = cursor.fetchone()
        exists = result[0] > 0

        if exists:
            logging.info("目标表 douyin_video_data 存在")
        else:
            logging.error("目标表 douyin_video_data 不存在")

        cursor.close()
        return exists

    except Exception as e:
        logging.error(f"检查表存在性失败: {e}")
        return False

def insert_data_to_mysql(df: pd.DataFrame, connection: pymysql.Connection) -> int:
    """
    将数据批量插入到MySQL数据库

    Args:
        df (pd.DataFrame): 要插入的数据
        connection (pymysql.Connection): 数据库连接

    Returns:
        int: 成功插入的记录数
    """
    if df.empty:
        logging.warning("没有数据需要插入")
        return 0

    try:
        cursor = connection.cursor()

        # 准备插入SQL语句（不包含is_related字段，使用默认值）
        insert_sql = """
        INSERT INTO douyin_video_data
        (search_topic, video_id, video_title_description, author_nickname,
         author_douyin_id, like_count, comment_count, share_count, play_count,
         publish_time, video_link, create_time)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        search_topic = VALUES(search_topic),
        video_title_description = VALUES(video_title_description),
        author_nickname = VALUES(author_nickname),
        author_douyin_id = VALUES(author_douyin_id),
        like_count = VALUES(like_count),
        comment_count = VALUES(comment_count),
        share_count = VALUES(share_count),
        play_count = VALUES(play_count),
        publish_time = VALUES(publish_time),
        video_link = VALUES(video_link)
        """

        # 批量处理数据
        batch_size = DATA_CONFIG.get('batch_size', 1000)
        total_records = len(df)
        total_inserted = 0

        logging.info(f"开始批量插入数据，总记录数: {total_records}，批次大小: {batch_size}")

        for i in range(0, total_records, batch_size):
            batch_df = df.iloc[i:i+batch_size]
            batch_data = []

            for _, row in batch_df.iterrows():
                # 处理每个字段的NaN值
                def clean_value(value):
                    if pd.isna(value):
                        return None
                    elif isinstance(value, str) and value.lower() == 'nan':
                        return None
                    return value

                batch_data.append((
                    clean_value(row['search_topic']) or '',
                    int(row['video_id']),
                    clean_value(row['video_title_description']) or '',
                    clean_value(row['author_nickname']) or '',
                    clean_value(row['author_douyin_id']),
                    int(row['like_count']),
                    int(row['comment_count']),
                    int(row['share_count']),
                    int(row['play_count']),
                    clean_value(row['publish_time']),
                    clean_value(row['video_link']) or '',
                    row['create_time']
                ))

            try:
                # 批量插入当前批次
                cursor.executemany(insert_sql, batch_data)
                connection.commit()

                batch_inserted = cursor.rowcount
                total_inserted += batch_inserted

                logging.info(f"批次 {i//batch_size + 1}: 成功插入 {batch_inserted} 条记录 "
                           f"(进度: {min(i+batch_size, total_records)}/{total_records})")

            except pymysql.Error as e:
                connection.rollback()
                logging.error(f"批次 {i//batch_size + 1} 插入失败: {e}")
                # 尝试逐条插入以找出问题记录
                logging.info("尝试逐条插入以识别问题记录...")
                for j, single_data in enumerate(batch_data):
                    try:
                        cursor.execute(insert_sql, single_data)
                        connection.commit()
                        total_inserted += 1
                    except pymysql.Error as single_e:
                        logging.error(f"记录 {i+j+1} 插入失败: {single_e}")
                        logging.error(f"问题数据: video_id={single_data[1]}")
                        connection.rollback()

        logging.info(f"数据插入完成，总共成功插入 {total_inserted} 条记录")
        return total_inserted

    except Exception as e:
        connection.rollback()
        logging.error(f"数据插入过程发生错误: {e}")
        raise
    finally:
        cursor.close()

def print_summary_statistics(df: pd.DataFrame) -> None:
    """
    打印数据统计摘要

    Args:
        df (pd.DataFrame): 数据框
    """
    logging.info("=== 数据统计摘要 ===")
    logging.info(f"总记录数: {len(df)}")
    logging.info(f"唯一视频ID数: {df['video_id'].nunique()}")
    logging.info(f"搜索话题数: {df['search_topic'].nunique()}")
    logging.info(f"作者数量: {df['author_nickname'].nunique()}")

    # 统计数值字段
    numeric_stats = df[['like_count', 'comment_count', 'share_count', 'play_count']].describe()
    logging.info("数值字段统计:")
    for col in numeric_stats.columns:
        logging.info(f"  {col}: 平均={numeric_stats.loc['mean', col]:.1f}, "
                    f"最大={numeric_stats.loc['max', col]:.0f}")

    # 时间范围
    if not df['publish_time'].isna().all():
        min_time = df['publish_time'].min()
        max_time = df['publish_time'].max()
        logging.info(f"发布时间范围: {min_time} 到 {max_time}")

def main():
    """
    主函数：协调整个数据导入流程
    """
    start_time = datetime.now()
    logging.info("=" * 60)
    logging.info("抖音视频数据导入工具启动")
    logging.info(f"启动时间: {start_time}")
    logging.info("=" * 60)

    try:
        # 检查CSV文件是否存在
        csv_file_path = CSV_CONFIG.get('file_path', 'liandong_search_results_1753841820642.csv')
        if not os.path.exists(csv_file_path):
            logging.error(f"CSV文件不存在: {csv_file_path}")
            logging.error("请确保CSV文件在正确的路径下")
            return False

        logging.info(f"找到CSV文件: {csv_file_path}")

        # 测试数据库连接
        logging.info("测试数据库连接...")
        if not test_database_connection():
            logging.error("数据库连接失败，请检查配置信息")
            return False

        # 读取CSV文件
        logging.info("正在读取CSV文件...")
        try:
            df = pd.read_csv(csv_file_path, encoding=CSV_CONFIG.get('encoding', 'utf-8'))
            logging.info(f"成功读取CSV文件，共 {len(df)} 条记录")
        except UnicodeDecodeError:
            logging.warning("UTF-8编码读取失败，尝试使用GBK编码...")
            df = pd.read_csv(csv_file_path, encoding='gbk')
            logging.info(f"使用GBK编码成功读取CSV文件，共 {len(df)} 条记录")

        # 验证CSV结构
        if not validate_csv_structure(df):
            logging.error("CSV文件结构验证失败")
            return False

        # 清洗和转换数据
        logging.info("正在清洗和转换数据...")
        df_cleaned = clean_and_convert_data(df)

        if df_cleaned.empty:
            logging.error("清洗后没有有效数据，导入终止")
            return False

        # 打印统计摘要
        print_summary_statistics(df_cleaned)

        # 连接数据库
        logging.info("正在连接数据库...")
        connection = connect_to_database()

        try:
            # 检查目标表是否存在
            if not check_table_exists(connection):
                logging.error("目标表不存在，请先创建表结构")
                return False

            # 插入数据
            logging.info("正在插入数据到数据库...")
            inserted_count = insert_data_to_mysql(df_cleaned, connection)

            end_time = datetime.now()
            duration = end_time - start_time

            logging.info("=" * 60)
            logging.info("数据导入完成！")
            logging.info(f"处理记录数: {len(df)} -> {len(df_cleaned)}")
            logging.info(f"成功插入: {inserted_count} 条记录")
            logging.info(f"总耗时: {duration}")
            logging.info(f"完成时间: {end_time}")
            logging.info("=" * 60)

            return True

        finally:
            connection.close()
            logging.info("数据库连接已关闭")

    except KeyboardInterrupt:
        logging.info("用户中断操作")
        return False
    except Exception as e:
        logging.error(f"数据导入过程中发生错误: {e}")
        logging.exception("详细错误信息:")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
