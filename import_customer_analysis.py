#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户分析数据导入MySQL数据库脚本
将customer_analysis_1753851782825.csv文件中的客户分析数据导入到MySQL数据库的customer_analysis_results表中

使用方法:
1. 确保config_template.py中的数据库配置正确
2. 确保CSV文件在正确路径
3. 运行: python import_customer_analysis.py

作者: AI Assistant
创建时间: 2025-07-30
"""

import pandas as pd
import pymysql
import logging
from datetime import datetime
import sys
import os
from typing import Optional
from decimal import Decimal

# 尝试导入配置文件，如果不存在则使用默认配置
try:
    from config_template import DB_CONFIG, LOG_CONFIG, DATA_CONFIG
    print("使用配置文件 config_template.py")
except ImportError:
    print("未找到配置文件，使用默认配置")
    # 默认数据库配置 - 请根据实际情况修改
    DB_CONFIG = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',          # 请修改为实际用户名
        'password': '',          # 请修改为实际密码
        'database': 'dyredbook',
        'charset': 'utf8mb4'
    }

    LOG_CONFIG = {
        'level': 'INFO',
        'file_name': 'customer_analysis_import.log'
    }

    DATA_CONFIG = {
        'batch_size': 1000,
        'skip_duplicates': True
    }

# CSV文件配置
CSV_FILE_PATH = 'customer_analysis_1753851782825.csv'

# 配置日志
log_level = getattr(logging, LOG_CONFIG.get('level', 'INFO').upper())
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_CONFIG.get('file_name', 'customer_analysis_import.log'), encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def test_database_connection() -> bool:
    """
    测试数据库连接

    Returns:
        bool: 连接是否成功
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        cursor.close()
        connection.close()
        logging.info("数据库连接测试成功")
        return True
    except Exception as e:
        logging.error(f"数据库连接测试失败: {e}")
        return False

def connect_to_database() -> pymysql.Connection:
    """
    连接到MySQL数据库

    Returns:
        pymysql.Connection: 数据库连接对象
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logging.info("数据库连接成功")
        return connection
    except pymysql.Error as e:
        logging.error(f"MySQL数据库连接失败: {e}")
        raise
    except Exception as e:
        logging.error(f"数据库连接失败: {e}")
        raise

def validate_csv_structure(df: pd.DataFrame) -> bool:
    """
    验证CSV文件结构是否正确

    Args:
        df (pd.DataFrame): CSV数据

    Returns:
        bool: 结构是否正确
    """
    expected_columns = [
        '用户昵称', '抖音号', 'SecUID', '用户ID', '意向类型', '置信度',
        '分析原因', '评论内容', '评论数量', '评论时间', 'IP地址', 
        '最新评论时间', '相关视频链接'
    ]

    if list(df.columns) != expected_columns:
        logging.error(f"CSV文件列结构不正确")
        logging.error(f"期望的列: {expected_columns}")
        logging.error(f"实际的列: {list(df.columns)}")
        return False

    logging.info("CSV文件结构验证通过")
    return True

def clean_and_convert_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    清洗和转换CSV数据以匹配数据库表结构

    Args:
        df (pd.DataFrame): 原始CSV数据

    Returns:
        pd.DataFrame: 清洗后的数据
    """
    try:
        logging.info("开始数据清洗和转换...")
        original_count = len(df)

        # 重命名列以匹配数据库字段
        column_mapping = {
            '用户昵称': 'user_nickname',
            '抖音号': 'douyin_id',
            'SecUID': 'sec_uid',
            '用户ID': 'user_id',
            '意向类型': 'intent_type',
            '置信度': 'confidence',
            '分析原因': 'analysis_reason',
            '评论内容': 'comment_content',
            '评论数量': 'comment_count',
            '评论时间': 'comment_times',
            'IP地址': 'ip_addresses',
            '最新评论时间': 'latest_comment_time',
            '相关视频链接': 'related_video_links'
        }

        df = df.rename(columns=column_mapping)

        # 删除关键字段为空的记录
        df = df.dropna(subset=['user_nickname', 'douyin_id'])
        logging.info(f"删除关键字段为空的记录后，剩余 {len(df)} 条记录")

        # 处理字符串字段长度限制
        string_fields = {
            'user_nickname': 255,
            'douyin_id': 100,
            'sec_uid': 255,
            'user_id': 50,
            'intent_type': 50
        }

        for field, max_length in string_fields.items():
            if field in df.columns:
                df[field] = df[field].astype(str).str[:max_length]
                df[field] = df[field].replace('nan', '')

        # 处理confidence字段（decimal类型）
        df['confidence'] = pd.to_numeric(df['confidence'], errors='coerce')
        df['confidence'] = df['confidence'].clip(lower=0, upper=1)  # 限制在0-1之间

        # 处理comment_count字段（整数类型）
        df['comment_count'] = pd.to_numeric(df['comment_count'], errors='coerce').fillna(0)
        df['comment_count'] = df['comment_count'].clip(lower=0).astype('int64')

        # 处理文本字段
        text_fields = ['analysis_reason', 'comment_content', 'comment_times', 
                      'ip_addresses', 'related_video_links']
        for field in text_fields:
            if field in df.columns:
                df[field] = df[field].fillna('')

        # 转换日期时间格式
        logging.info("转换最新评论时间格式...")
        df['latest_comment_time'] = pd.to_datetime(df['latest_comment_time'], 
                                                  format='%Y-%m-%d %H:%M:%S', errors='coerce')
        invalid_dates = df['latest_comment_time'].isna().sum()
        if invalid_dates > 0:
            logging.warning(f"有 {invalid_dates} 条记录的最新评论时间格式无效")

        # 添加时间戳字段
        current_time = datetime.now()
        df['import_time'] = current_time
        df['created_at'] = current_time

        # 添加默认值字段
        df['signature'] = None  # 保持NULL，什么都不做
        df['status'] = '0'      # 设置为字符串'0'作为默认状态

        logging.info(f"数据清洗完成，原始记录: {original_count}，最终有效记录: {len(df)}")
        return df

    except Exception as e:
        logging.error(f"数据清洗失败: {e}")
        raise

def check_table_exists(connection: pymysql.Connection) -> bool:
    """
    检查目标表是否存在

    Args:
        connection (pymysql.Connection): 数据库连接

    Returns:
        bool: 表是否存在
    """
    try:
        cursor = connection.cursor()
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = %s AND table_name = 'customer_analysis_results'
        """, (DB_CONFIG['database'],))

        result = cursor.fetchone()
        exists = result[0] > 0

        if exists:
            logging.info("目标表 customer_analysis_results 存在")
        else:
            logging.error("目标表 customer_analysis_results 不存在")

        cursor.close()
        return exists

    except Exception as e:
        logging.error(f"检查表存在性失败: {e}")
        return False

def insert_data_to_mysql(df: pd.DataFrame, connection: pymysql.Connection) -> int:
    """
    将数据批量插入到MySQL数据库

    Args:
        df (pd.DataFrame): 要插入的数据
        connection (pymysql.Connection): 数据库连接

    Returns:
        int: 成功插入的记录数
    """
    if df.empty:
        logging.warning("没有数据需要插入")
        return 0

    try:
        cursor = connection.cursor()

        # 准备插入SQL语句
        insert_sql = """
        INSERT INTO customer_analysis_results
        (user_nickname, douyin_id, sec_uid, user_id, intent_type, confidence,
         analysis_reason, comment_content, comment_count, comment_times,
         ip_addresses, latest_comment_time, related_video_links,
         import_time, created_at, signature, status)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        # 批量处理数据
        batch_size = DATA_CONFIG.get('batch_size', 1000)
        total_records = len(df)
        total_inserted = 0

        logging.info(f"开始批量插入数据，总记录数: {total_records}，批次大小: {batch_size}")

        for i in range(0, total_records, batch_size):
            batch_df = df.iloc[i:i+batch_size]
            batch_data = []

            for _, row in batch_df.iterrows():
                # 处理每个字段的NaN值
                def clean_value(value):
                    if pd.isna(value):
                        return None
                    elif isinstance(value, str) and value.lower() == 'nan':
                        return None
                    return value

                batch_data.append((
                    clean_value(row['user_nickname']) or '',
                    clean_value(row['douyin_id']) or '',
                    clean_value(row['sec_uid']) or '',
                    clean_value(row['user_id']) or '',
                    clean_value(row['intent_type']) or '',
                    float(row['confidence']) if not pd.isna(row['confidence']) else None,
                    clean_value(row['analysis_reason']) or '',
                    clean_value(row['comment_content']) or '',
                    int(row['comment_count']),
                    clean_value(row['comment_times']) or '',
                    clean_value(row['ip_addresses']) or '',
                    clean_value(row['latest_comment_time']),
                    clean_value(row['related_video_links']) or '',
                    row['import_time'],
                    row['created_at'],
                    None,  # signature - 保持NULL
                    '0'    # status - 字符串'0'
                ))

            try:
                # 批量插入当前批次
                cursor.executemany(insert_sql, batch_data)
                connection.commit()

                batch_inserted = cursor.rowcount
                total_inserted += batch_inserted

                logging.info(f"批次 {i//batch_size + 1}: 成功插入 {batch_inserted} 条记录 "
                           f"(进度: {min(i+batch_size, total_records)}/{total_records})")

            except pymysql.Error as e:
                connection.rollback()
                logging.error(f"批次 {i//batch_size + 1} 插入失败: {e}")
                # 尝试逐条插入以找出问题记录
                logging.info("尝试逐条插入以识别问题记录...")
                for j, single_data in enumerate(batch_data):
                    try:
                        cursor.execute(insert_sql, single_data)
                        connection.commit()
                        total_inserted += 1
                    except pymysql.Error as single_e:
                        logging.error(f"记录 {i+j+1} 插入失败: {single_e}")
                        logging.error(f"问题数据: user_nickname={single_data[0]}, douyin_id={single_data[1]}")
                        connection.rollback()

        logging.info(f"数据插入完成，总共成功插入 {total_inserted} 条记录")
        return total_inserted

    except Exception as e:
        connection.rollback()
        logging.error(f"数据插入过程发生错误: {e}")
        raise
    finally:
        cursor.close()

def print_summary_statistics(df: pd.DataFrame) -> None:
    """
    打印数据统计摘要

    Args:
        df (pd.DataFrame): 数据框
    """
    logging.info("=== 客户分析数据统计摘要 ===")
    logging.info(f"总记录数: {len(df)}")
    logging.info(f"唯一用户数: {df['user_nickname'].nunique()}")
    logging.info(f"唯一抖音号数: {df['douyin_id'].nunique()}")

    # 意向类型统计
    if 'intent_type' in df.columns:
        intent_counts = df['intent_type'].value_counts()
        logging.info("意向类型分布:")
        for intent, count in intent_counts.items():
            logging.info(f"  {intent}: {count} 条")

    # 置信度统计
    if 'confidence' in df.columns and not df['confidence'].isna().all():
        confidence_stats = df['confidence'].describe()
        logging.info(f"置信度统计: 平均={confidence_stats['mean']:.3f}, "
                    f"最小={confidence_stats['min']:.3f}, 最大={confidence_stats['max']:.3f}")

    # 评论数量统计
    if 'comment_count' in df.columns:
        comment_stats = df['comment_count'].describe()
        logging.info(f"评论数量统计: 平均={comment_stats['mean']:.1f}, "
                    f"最大={comment_stats['max']:.0f}")

def main():
    """
    主函数：协调整个数据导入流程
    """
    start_time = datetime.now()
    logging.info("=" * 60)
    logging.info("客户分析数据导入工具启动")
    logging.info(f"启动时间: {start_time}")
    logging.info("=" * 60)

    try:
        # 检查CSV文件是否存在
        if not os.path.exists(CSV_FILE_PATH):
            logging.error(f"CSV文件不存在: {CSV_FILE_PATH}")
            logging.error("请确保CSV文件在正确的路径下")
            return False

        logging.info(f"找到CSV文件: {CSV_FILE_PATH}")

        # 测试数据库连接
        logging.info("测试数据库连接...")
        if not test_database_connection():
            logging.error("数据库连接失败，请检查配置信息")
            return False

        # 读取CSV文件
        logging.info("正在读取CSV文件...")
        try:
            df = pd.read_csv(CSV_FILE_PATH, encoding='utf-8')
            logging.info(f"成功读取CSV文件，共 {len(df)} 条记录")
        except UnicodeDecodeError:
            logging.warning("UTF-8编码读取失败，尝试使用GBK编码...")
            df = pd.read_csv(CSV_FILE_PATH, encoding='gbk')
            logging.info(f"使用GBK编码成功读取CSV文件，共 {len(df)} 条记录")

        # 验证CSV结构
        if not validate_csv_structure(df):
            logging.error("CSV文件结构验证失败")
            return False

        # 清洗和转换数据
        logging.info("正在清洗和转换数据...")
        df_cleaned = clean_and_convert_data(df)

        if df_cleaned.empty:
            logging.error("清洗后没有有效数据，导入终止")
            return False

        # 打印统计摘要
        print_summary_statistics(df_cleaned)

        # 连接数据库
        logging.info("正在连接数据库...")
        connection = connect_to_database()

        try:
            # 检查目标表是否存在
            if not check_table_exists(connection):
                logging.error("目标表不存在，请先创建表结构")
                return False

            # 插入数据
            logging.info("正在插入数据到数据库...")
            inserted_count = insert_data_to_mysql(df_cleaned, connection)

            end_time = datetime.now()
            duration = end_time - start_time

            logging.info("=" * 60)
            logging.info("客户分析数据导入完成！")
            logging.info(f"处理记录数: {len(df)} -> {len(df_cleaned)}")
            logging.info(f"成功插入: {inserted_count} 条记录")
            logging.info(f"总耗时: {duration}")
            logging.info(f"完成时间: {end_time}")
            logging.info("=" * 60)

            return True

        finally:
            connection.close()
            logging.info("数据库连接已关闭")

    except KeyboardInterrupt:
        logging.info("用户中断操作")
        return False
    except Exception as e:
        logging.error(f"数据导入过程中发生错误: {e}")
        logging.exception("详细错误信息:")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
